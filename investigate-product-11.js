/**
 * Investigation script for Product ID 11 (Barcode: 454545)
 * Checking why it's not appearing in Reorder Level report
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function investigateProduct11() {
    console.log('🔍 Investigating Product ID 11 (Barcode: 454545)');
    console.log('==================================================\n');

    const dbPath = path.join(__dirname, 'src', 'pos_system.db');
    console.log(`Database path: ${dbPath}`);
    
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('❌ Database connection failed:', err.message);
                reject(err);
                return;
            }
            
            console.log('✅ Connected to SQLite database\n');
            
            // Step 1: Find the specific product
            console.log('🔍 Step 1: Looking for Product ID 11');
            console.log('-'.repeat(50));
            
            db.get(`SELECT * FROM products WHERE id = 11`, [], (err, product) => {
                if (err) {
                    console.error('❌ Error querying product:', err.message);
                    db.close();
                    reject(err);
                    return;
                }
                
                if (!product) {
                    console.log('❌ Product ID 11 not found in products table');
                    
                    // Check if there's a product with barcode 454545
                    console.log('\n🔍 Checking for barcode 454545...');
                    db.get(`SELECT * FROM products WHERE barcode = '454545'`, [], (err, barcodeProduct) => {
                        if (err) {
                            console.error('❌ Error querying by barcode:', err.message);
                            db.close();
                            reject(err);
                            return;
                        }
                        
                        if (barcodeProduct) {
                            console.log('✅ Found product with barcode 454545:');
                            console.log(`  ID: ${barcodeProduct.id}`);
                            console.log(`  Barcode: ${barcodeProduct.barcode}`);
                            console.log(`  Description: ${barcodeProduct.description}`);
                            console.log(`  Min Qty: ${barcodeProduct.min_qty}`);
                            console.log(`  Max Qty: ${barcodeProduct.max_qty}`);
                        } else {
                            console.log('❌ No product found with barcode 454545 either');
                        }
                        
                        db.close();
                        resolve();
                    });
                    return;
                }
                
                console.log('✅ Found Product ID 11:');
                console.log(`  ID: ${product.id}`);
                console.log(`  Barcode: ${product.barcode}`);
                console.log(`  Description: ${product.description}`);
                console.log(`  Category: ${product.category || 'N/A'}`);
                console.log(`  Min Qty: ${product.min_qty}`);
                console.log(`  Max Qty: ${product.max_qty}`);
                console.log(`  Created: ${product.created_at}`);
                
                // Step 2: Check location_stocks for this product
                console.log('\n🔍 Step 2: Checking location_stocks for Product ID 11');
                console.log('-'.repeat(50));
                
                db.all(`SELECT * FROM location_stocks WHERE product_id = 11`, [], (err, locationStocks) => {
                    if (err) {
                        console.error('❌ Error querying location_stocks:', err.message);
                        db.close();
                        reject(err);
                        return;
                    }
                    
                    if (locationStocks.length === 0) {
                        console.log('❌ No location_stocks entries found for Product ID 11');
                        console.log('   This is likely why it\'s not appearing in reorder reports!');
                        console.log('   The reorder query uses INNER JOIN with location_stocks');
                    } else {
                        console.log(`✅ Found ${locationStocks.length} location_stocks entries:`);
                        locationStocks.forEach((stock, index) => {
                            console.log(`\n  Stock Entry ${index + 1}:`);
                            console.log(`    Product ID: ${stock.product_id}`);
                            console.log(`    Location: ${stock.location}`);
                            console.log(`    Stock: ${stock.stock}`);
                            console.log(`    Price: ${stock.price || 'N/A'}`);
                            console.log(`    Min Qty: ${stock.min_qty || 'NULL'}`);
                            console.log(`    Max Qty: ${stock.max_qty || 'NULL'}`);
                        });
                    }
                    
                    // Step 3: Test the reorder query specifically for this product
                    console.log('\n🔍 Step 3: Testing Reorder Query for Product ID 11');
                    console.log('-'.repeat(50));
                    
                    const reorderQuery = `
                        SELECT
                            p.id,
                            p.barcode,
                            p.description,
                            p.min_qty as product_min_qty,
                            p.max_qty as product_max_qty,
                            ls.location,
                            COALESCE(ls.stock, 0) as current_stock,
                            COALESCE(p.min_qty, 0) as min_qty_for_comparison,
                            CASE
                                WHEN COALESCE(ls.stock, 0) <= COALESCE(p.min_qty, 0) THEN 'critical'
                                WHEN COALESCE(ls.stock, 0) <= (COALESCE(p.min_qty, 0) + 5) THEN 'low'
                                ELSE 'normal'
                            END as stock_status,
                            (COALESCE(ls.stock, 0) <= COALESCE(p.min_qty, 0)) as should_appear_in_reorder
                        FROM products p
                        LEFT JOIN location_stocks ls ON p.id = ls.product_id
                        WHERE p.id = 11
                    `;
                    
                    db.all(reorderQuery, [], (err, reorderResults) => {
                        if (err) {
                            console.error('❌ Error in reorder query:', err.message);
                            db.close();
                            reject(err);
                            return;
                        }
                        
                        console.log(`Query returned ${reorderResults.length} results:`);
                        reorderResults.forEach((result, index) => {
                            console.log(`\n  Result ${index + 1}:`);
                            console.log(`    Product ID: ${result.id}`);
                            console.log(`    Barcode: ${result.barcode}`);
                            console.log(`    Description: ${result.description}`);
                            console.log(`    Location: ${result.location || 'No location'}`);
                            console.log(`    Current Stock: ${result.current_stock}`);
                            console.log(`    Min Qty: ${result.min_qty_for_comparison}`);
                            console.log(`    Status: ${result.stock_status}`);
                            console.log(`    Should appear in reorder: ${result.should_appear_in_reorder}`);
                            console.log(`    Logic: ${result.current_stock} <= ${result.min_qty_for_comparison} = ${result.should_appear_in_reorder}`);
                        });
                        
                        // Step 4: Test the actual InventoryService query (with INNER JOIN)
                        console.log('\n🔍 Step 4: Testing Actual InventoryService Query (INNER JOIN)');
                        console.log('-'.repeat(50));
                        
                        const inventoryServiceQuery = `
                            SELECT
                                p.id,
                                p.barcode,
                                p.description,
                                p.category,
                                p.subcategory,
                                p.supplier,
                                p.min_qty as product_min_qty,
                                p.max_qty,
                                ls.location,
                                COALESCE(ls.stock, 0) as stock,
                                COALESCE(p.min_qty, 0) as min_qty,
                                COALESCE(ls.price, 0) as price,
                                CASE
                                    WHEN COALESCE(ls.stock, 0) <= COALESCE(p.min_qty, 0) THEN 'critical'
                                    WHEN COALESCE(ls.stock, 0) <= (COALESCE(p.min_qty, 0) + 5) THEN 'low'
                                    ELSE 'normal'
                                END as stock_status
                            FROM products p
                            INNER JOIN location_stocks ls ON p.id = ls.product_id
                            WHERE p.id = 11
                            AND COALESCE(p.min_qty, 0) > 0
                            AND (COALESCE(ls.stock, 0) <= COALESCE(p.min_qty, 0) OR COALESCE(ls.stock, 0) <= (COALESCE(p.min_qty, 0) + 5))
                        `;
                        
                        db.all(inventoryServiceQuery, [], (err, inventoryResults) => {
                            if (err) {
                                console.error('❌ Error in inventory service query:', err.message);
                                db.close();
                                reject(err);
                                return;
                            }
                            
                            console.log(`InventoryService query returned ${inventoryResults.length} results:`);
                            if (inventoryResults.length > 0) {
                                inventoryResults.forEach((result, index) => {
                                    console.log(`\n  Result ${index + 1}:`);
                                    console.log(`    Product ID: ${result.id}`);
                                    console.log(`    Barcode: ${result.barcode}`);
                                    console.log(`    Description: ${result.description}`);
                                    console.log(`    Location: ${result.location}`);
                                    console.log(`    Current Stock: ${result.stock}`);
                                    console.log(`    Min Qty: ${result.min_qty}`);
                                    console.log(`    Status: ${result.stock_status}`);
                                });
                            } else {
                                console.log('❌ Product ID 11 not returned by InventoryService query');
                                console.log('   Possible reasons:');
                                console.log('   1. No entry in location_stocks table (INNER JOIN fails)');
                                console.log('   2. min_qty is 0 or NULL');
                                console.log('   3. Stock level doesn\'t meet reorder criteria');
                            }
                            
                            // Step 5: Diagnosis and recommendations
                            console.log('\n📊 Diagnosis and Recommendations:');
                            console.log('='.repeat(50));
                            
                            if (locationStocks.length === 0) {
                                console.log('🚨 ROOT CAUSE IDENTIFIED:');
                                console.log('   Product ID 11 has NO entries in location_stocks table');
                                console.log('   The reorder query uses INNER JOIN with location_stocks');
                                console.log('   Without location_stocks entry, product cannot appear in reorder report');
                                console.log('');
                                console.log('💡 SOLUTION:');
                                console.log('   Add an entry to location_stocks table for Product ID 11');
                                console.log('   Example SQL:');
                                console.log(`   INSERT INTO location_stocks (product_id, location, stock, price)`);
                                console.log(`   VALUES (11, 'Default Location', 3, 0.00);`);
                            } else {
                                console.log('✅ Product has location_stocks entries');
                                if (inventoryResults.length === 0) {
                                    console.log('🚨 Product not meeting reorder criteria in InventoryService query');
                                    console.log('   Check min_qty and stock level conditions');
                                } else {
                                    console.log('✅ Product should appear in reorder report');
                                    console.log('   Issue might be in frontend display logic');
                                }
                            }
                            
                            db.close();
                            resolve();
                        });
                    });
                });
            });
        });
    });
}

// Run the investigation
investigateProduct11().catch(console.error);
