/**
 * Test the IPC call that the frontend makes to get reorder alerts
 * This simulates what happens when the admin panel loads reorder data
 */

const { ipcMain } = require('electron');
const Database = require('./src/database');
const InventoryService = require('./src/services/inventoryService');
const path = require('path');

// Mock current user (admin)
const mockCurrentUser = {
    id: 1,
    username: 'admin',
    role: 'Admin',
    location_id: 4,
    location_name: 'Taboo-Atlantic City-NJ'
};

async function testIPCReorderCall() {
    console.log('🧪 Testing IPC Reorder Call (Simulating Frontend Request)');
    console.log('=========================================================\n');

    // Initialize database and inventory service
    const dbPath = path.join(__dirname, 'src', 'pos_system.db');
    console.log(`Database path: ${dbPath}`);
    
    const mockDb = {
        db: null
    };
    
    const sqlite3 = require('sqlite3').verbose();
    
    return new Promise((resolve, reject) => {
        mockDb.db = new sqlite3.Database(dbPath, async (err) => {
            if (err) {
                console.error('❌ Database connection failed:', err.message);
                reject(err);
                return;
            }
            
            console.log('✅ Connected to SQLite database\n');
            
            try {
                // Initialize inventory service
                const inventoryService = new InventoryService(mockDb);
                
                console.log('🔍 Step 1: Simulating IPC Handler Logic');
                console.log('-'.repeat(50));
                console.log('Current User:', mockCurrentUser);
                
                // Replicate the exact logic from the IPC handler
                const locationName = mockCurrentUser.role === 'Admin' ? null : mockCurrentUser.location_name;
                console.log('Location filter:', locationName || 'All locations (Admin)');
                
                const report = await inventoryService.getReorderReport(locationName);
                
                console.log('\n📊 Raw Report from InventoryService:');
                console.log(`  Timestamp: ${report.timestamp}`);
                console.log(`  Location: ${report.location}`);
                console.log(`  Total products checked: ${report.total_products_checked}`);
                console.log(`  Critical stock count: ${report.critical_stock.length}`);
                console.log(`  Low stock count: ${report.low_stock.length}`);
                console.log(`  Total alerts: ${report.alerts.length}`);
                
                // Check for Product ID 11 in raw report
                const product11Raw = report.alerts.find(alert => alert.id === 11);
                console.log(`\n🔍 Product ID 11 in raw report: ${product11Raw ? 'FOUND' : 'NOT FOUND'}`);
                if (product11Raw) {
                    console.log('  Raw Product 11 data:', {
                        id: product11Raw.id,
                        barcode: product11Raw.barcode,
                        description: product11Raw.description,
                        stock: product11Raw.stock,
                        min_qty: product11Raw.min_qty,
                        status: product11Raw.stock_status
                    });
                }
                
                // Replicate the simplification logic from IPC handler
                console.log('\n🔄 Step 2: Applying IPC Handler Simplification');
                console.log('-'.repeat(50));
                
                const simplifiedReport = {
                    timestamp: report.timestamp,
                    location: report.location,
                    total_products_checked: report.total_products_checked,
                    critical_stock_count: report.critical_stock.length,
                    low_stock_count: report.low_stock.length,
                    total_reorder_value: report.total_reorder_value,
                    alerts: report.alerts.map(alert => ({
                        id: alert.id,
                        barcode: alert.barcode,
                        description: alert.description,
                        category: alert.category,
                        subcategory: alert.subcategory,
                        supplier: alert.supplier,
                        location: alert.location,
                        stock: alert.stock,
                        min_qty: alert.min_qty,
                        max_qty: alert.max_qty,
                        price: alert.price,
                        stock_status: alert.stock_status,
                        reorder_quantity: alert.reorder_quantity,
                        days_until_stockout: alert.days_until_stockout,
                        last_checked: alert.last_checked
                    }))
                };
                
                console.log('📦 Simplified Report (What Frontend Receives):');
                console.log(`  Critical stock count: ${simplifiedReport.critical_stock_count}`);
                console.log(`  Low stock count: ${simplifiedReport.low_stock_count}`);
                console.log(`  Total alerts: ${simplifiedReport.alerts.length}`);
                
                // Check for Product ID 11 in simplified report
                const product11Simplified = simplifiedReport.alerts.find(alert => alert.id === 11);
                console.log(`\n🔍 Product ID 11 in simplified report: ${product11Simplified ? 'FOUND' : 'NOT FOUND'}`);
                if (product11Simplified) {
                    console.log('  Simplified Product 11 data:', {
                        id: product11Simplified.id,
                        barcode: product11Simplified.barcode,
                        description: product11Simplified.description,
                        stock: product11Simplified.stock,
                        min_qty: product11Simplified.min_qty,
                        status: product11Simplified.stock_status
                    });
                }
                
                // Show all alerts for comparison
                console.log('\n📋 All Alerts in Simplified Report:');
                console.log('-'.repeat(50));
                if (simplifiedReport.alerts.length > 0) {
                    simplifiedReport.alerts.forEach((alert, index) => {
                        console.log(`\n  Alert ${index + 1}:`);
                        console.log(`    ID: ${alert.id}`);
                        console.log(`    Barcode: ${alert.barcode}`);
                        console.log(`    Description: ${alert.description}`);
                        console.log(`    Location: ${alert.location}`);
                        console.log(`    Stock: ${alert.stock}`);
                        console.log(`    Min Qty: ${alert.min_qty}`);
                        console.log(`    Status: ${alert.stock_status}`);
                    });
                } else {
                    console.log('  No alerts found');
                }
                
                // Final IPC response simulation
                console.log('\n🎯 Final IPC Response:');
                console.log('-'.repeat(50));
                const ipcResponse = { success: true, data: simplifiedReport };
                console.log('IPC Response structure:', {
                    success: ipcResponse.success,
                    data: {
                        alerts_count: ipcResponse.data.alerts.length,
                        critical_count: ipcResponse.data.critical_stock_count,
                        low_count: ipcResponse.data.low_stock_count
                    }
                });
                
                // Summary
                console.log('\n📊 Investigation Summary:');
                console.log('='.repeat(50));
                console.log(`Product ID 11 Status:`);
                console.log(`  Expected: Should appear (Stock: 3, Min: 5)`);
                console.log(`  Raw Report: ${product11Raw ? 'FOUND' : 'NOT FOUND'}`);
                console.log(`  Simplified Report: ${product11Simplified ? 'FOUND' : 'NOT FOUND'}`);
                console.log(`  IPC Response: ${ipcResponse.success ? 'SUCCESS' : 'FAILED'}`);
                
                if (product11Raw && product11Simplified) {
                    console.log('\n✅ BACKEND AND IPC ARE WORKING CORRECTLY');
                    console.log('   Product ID 11 is being processed and sent to frontend');
                    console.log('   Issue must be in frontend display logic or user interaction');
                } else {
                    console.log('\n❌ ISSUE IDENTIFIED IN BACKEND/IPC');
                    console.log('   Product ID 11 is not being processed correctly');
                }
                
                mockDb.db.close();
                resolve();
                
            } catch (error) {
                console.error('❌ Error during testing:', error);
                mockDb.db.close();
                reject(error);
            }
        });
    });
}

// Run the test
testIPCReorderCall().catch(console.error);
