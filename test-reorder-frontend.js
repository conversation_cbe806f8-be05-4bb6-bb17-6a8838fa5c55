/**
 * Test the complete reorder system end-to-end to identify frontend issues
 */

const Database = require('./src/database');
const InventoryService = require('./src/services/inventoryService');
const path = require('path');

async function testReorderFrontend() {
    console.log('🧪 Testing Complete Reorder System (Backend to Frontend)');
    console.log('========================================================\n');

    // Initialize database (without Electron dependencies)
    const dbPath = path.join(__dirname, 'src', 'pos_system.db');
    console.log(`Database path: ${dbPath}`);
    
    // Create a mock database instance that works without Electron
    const mockDb = {
        db: null
    };
    
    const sqlite3 = require('sqlite3').verbose();
    
    return new Promise((resolve, reject) => {
        mockDb.db = new sqlite3.Database(dbPath, async (err) => {
            if (err) {
                console.error('❌ Database connection failed:', err.message);
                reject(err);
                return;
            }
            
            console.log('✅ Connected to SQLite database\n');
            
            try {
                // Test InventoryService directly
                console.log('🔍 Step 1: Testing InventoryService.getReorderReport()');
                console.log('-'.repeat(60));
                
                const inventoryService = new InventoryService(mockDb);
                const reorderReport = await inventoryService.getReorderReport();
                
                console.log('📊 Reorder Report Results:');
                console.log(`  Timestamp: ${reorderReport.timestamp}`);
                console.log(`  Location: ${reorderReport.location}`);
                console.log(`  Total products checked: ${reorderReport.total_products_checked}`);
                console.log(`  Critical stock count: ${reorderReport.critical_stock.length}`);
                console.log(`  Low stock count: ${reorderReport.low_stock.length}`);
                console.log(`  Total reorder value: $${reorderReport.total_reorder_value.toFixed(2)}`);
                console.log(`  Total alerts: ${reorderReport.alerts.length}`);
                
                // Check specifically for Product ID 11
                console.log('\n🔍 Step 2: Looking for Product ID 11 in Results');
                console.log('-'.repeat(60));
                
                const product11Alert = reorderReport.alerts.find(alert => alert.id === 11);
                
                if (product11Alert) {
                    console.log('✅ Found Product ID 11 in reorder alerts:');
                    console.log(`  ID: ${product11Alert.id}`);
                    console.log(`  Barcode: ${product11Alert.barcode}`);
                    console.log(`  Description: ${product11Alert.description}`);
                    console.log(`  Location: ${product11Alert.location}`);
                    console.log(`  Current Stock: ${product11Alert.stock}`);
                    console.log(`  Min Qty: ${product11Alert.min_qty}`);
                    console.log(`  Status: ${product11Alert.stock_status}`);
                    console.log(`  Reorder Qty: ${product11Alert.reorder_quantity}`);
                } else {
                    console.log('❌ Product ID 11 NOT found in reorder alerts');
                    console.log('   This indicates a backend issue');
                }
                
                // Show all alerts for comparison
                console.log('\n📋 Step 3: All Reorder Alerts Found');
                console.log('-'.repeat(60));
                
                if (reorderReport.alerts.length > 0) {
                    reorderReport.alerts.forEach((alert, index) => {
                        console.log(`\n  Alert ${index + 1}:`);
                        console.log(`    ID: ${alert.id}`);
                        console.log(`    Barcode: ${alert.barcode}`);
                        console.log(`    Description: ${alert.description}`);
                        console.log(`    Location: ${alert.location}`);
                        console.log(`    Current Stock: ${alert.stock}`);
                        console.log(`    Min Qty: ${alert.min_qty}`);
                        console.log(`    Status: ${alert.stock_status}`);
                    });
                } else {
                    console.log('❌ No alerts found at all');
                }
                
                // Test the simplified report format (same as IPC handler)
                console.log('\n🔄 Step 4: Testing IPC Handler Format (What Frontend Receives)');
                console.log('-'.repeat(60));
                
                const simplifiedReport = {
                    timestamp: reorderReport.timestamp,
                    location: reorderReport.location,
                    total_products_checked: reorderReport.total_products_checked,
                    critical_stock_count: reorderReport.critical_stock.length,
                    low_stock_count: reorderReport.low_stock.length,
                    total_reorder_value: reorderReport.total_reorder_value,
                    alerts: reorderReport.alerts.map(alert => ({
                        id: alert.id,
                        barcode: alert.barcode,
                        description: alert.description,
                        category: alert.category,
                        subcategory: alert.subcategory,
                        supplier: alert.supplier,
                        location: alert.location,
                        stock: alert.stock,
                        min_qty: alert.min_qty,
                        max_qty: alert.max_qty,
                        price: alert.price,
                        stock_status: alert.stock_status,
                        reorder_quantity: alert.reorder_quantity,
                        days_until_stockout: alert.days_until_stockout,
                        last_checked: alert.last_checked
                    }))
                };
                
                console.log('📦 Simplified Report (IPC Response):');
                console.log(`  Critical stock count: ${simplifiedReport.critical_stock_count}`);
                console.log(`  Low stock count: ${simplifiedReport.low_stock_count}`);
                console.log(`  Total alerts: ${simplifiedReport.alerts.length}`);
                
                // Check for Product ID 11 in simplified format
                const product11Simplified = simplifiedReport.alerts.find(alert => alert.id === 11);
                
                if (product11Simplified) {
                    console.log('\n✅ Product ID 11 found in simplified format:');
                    console.log(`  ID: ${product11Simplified.id}`);
                    console.log(`  Barcode: ${product11Simplified.barcode}`);
                    console.log(`  Description: ${product11Simplified.description}`);
                    console.log(`  Stock: ${product11Simplified.stock}`);
                    console.log(`  Min Qty: ${product11Simplified.min_qty}`);
                    console.log(`  Status: ${product11Simplified.stock_status}`);
                } else {
                    console.log('\n❌ Product ID 11 NOT found in simplified format');
                }
                
                // Summary and next steps
                console.log('\n📊 Investigation Summary:');
                console.log('='.repeat(60));
                
                if (product11Alert && product11Simplified) {
                    console.log('✅ BACKEND IS WORKING CORRECTLY');
                    console.log('   Product ID 11 is being detected and processed');
                    console.log('   Issue is likely in the FRONTEND display logic');
                    console.log('');
                    console.log('🔍 Next Steps:');
                    console.log('   1. Check admin panel reorder level display');
                    console.log('   2. Verify IPC communication is working');
                    console.log('   3. Check frontend JavaScript for display issues');
                    console.log('   4. Verify user permissions and location filtering');
                } else {
                    console.log('❌ BACKEND ISSUE IDENTIFIED');
                    console.log('   Product ID 11 is not being detected by InventoryService');
                    console.log('   Need to investigate InventoryService.getReorderReport()');
                }
                
                console.log('\n🎯 Product ID 11 Status:');
                console.log(`   Expected: Should appear (Stock: 3, Min: 5)`);
                console.log(`   Backend: ${product11Alert ? 'FOUND' : 'NOT FOUND'}`);
                console.log(`   IPC Format: ${product11Simplified ? 'FOUND' : 'NOT FOUND'}`);
                
                mockDb.db.close();
                resolve();
                
            } catch (error) {
                console.error('❌ Error during testing:', error);
                mockDb.db.close();
                reject(error);
            }
        });
    });
}

// Run the test
testReorderFrontend().catch(console.error);
